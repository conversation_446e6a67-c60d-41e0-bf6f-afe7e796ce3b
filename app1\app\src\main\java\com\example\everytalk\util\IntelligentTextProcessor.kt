package com.example.everytalk.util

import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 智能文本处理器
 * 负责高级文本处理、智能预处理和渐进式矫正
 */
class IntelligentTextProcessor(
    private var formatConfig: FormatCorrectionConfig,
    private val performanceMetrics: PerformanceMetrics
) {
    private val logger = AppLogger.forComponent("IntelligentTextProcessor")
    
    // 缓存系统
    private val preprocessingCache = ConcurrentHashMap<String, String>()
    private val progressiveCache = ConcurrentHashMap<String, String>()
    
    // <think>标签处理状态
    private val thinkingBuffer = AtomicReference(StringBuilder())
    private val isInsideThinkTag = AtomicBoolean(false)
    private val hasFoundThinkTag = AtomicBoolean(false)
    
    /**
     * 更新配置
     */
    fun updateConfig(config: FormatCorrectionConfig) {
        this.formatConfig = config
    }
    
    /**
     * 超级智能预处理 - 最高级别的文本智能处理
     * 包含上下文感知、语义理解和自适应优化
     */
    fun superIntelligentPreprocessing(text: String): String {
        if (text.isBlank() || !formatConfig.enableIntelligentFormatting) return text
        
        // 检查缓存
        if (formatConfig.enableCaching) {
            val cacheKey = "super_${text.hashCode()}"
            preprocessingCache[cacheKey]?.let { cached ->
                performanceMetrics.cacheHits++
                return cached
            }
            performanceMetrics.cacheMisses++
        }
        
        return performanceOptimizedProcessing(text, "superIntelligentPreprocessing") {
            var processed = text
            
            // 1. 上下文感知处理
            processed = contextAwareProcessing(processed)
            
            // 2. 语义结构优化
            processed = semanticStructureOptimization(processed)
            
            // 3. 自适应格式调整
            processed = adaptiveFormatAdjustment(processed)
            
            // 4. 智能内容增强
            processed = intelligentContentEnhancement(processed)
            
            // 缓存结果
            if (formatConfig.enableCaching && text.length < 5000) {
                val cacheKey = "super_${text.hashCode()}"
                preprocessingCache[cacheKey] = processed
                cleanupCache()
            }
            
            processed
        }
    }
    
    /**
     * 上下文感知处理
     */
    private fun contextAwareProcessing(text: String): String {
        var processed = text
        
        // 检测内容类型并应用相应处理
        when (detectContentType(text)) {
            ContentType.CODE -> processed = processCodeContent(processed)
            ContentType.MATH -> processed = processMathContent(processed)
            ContentType.LIST -> processed = processListContent(processed)
            ContentType.TABLE -> processed = processTableContent(processed)
            ContentType.MIXED -> processed = processMixedContent(processed)
            ContentType.PLAIN -> processed = processPlainContent(processed)
        }
        
        return processed
    }
    
    /**
     * 语义结构优化
     */
    private fun semanticStructureOptimization(text: String): String {
        var optimized = text
        
        // 优化段落结构
        optimized = optimizeParagraphStructure(optimized)
        
        // 优化列表结构
        optimized = optimizeListStructure(optimized)
        
        // 优化代码块结构
        optimized = optimizeCodeBlockStructure(optimized)
        
        return optimized
    }
    
    /**
     * 自适应格式调整
     */
    private fun adaptiveFormatAdjustment(text: String): String {
        var adjusted = text
        
        // 根据文本长度调整处理策略
        when {
            text.length < 100 -> adjusted = lightweightFormatting(adjusted)
            text.length < 1000 -> adjusted = standardFormatting(adjusted)
            else -> adjusted = heavyweightFormatting(adjusted)
        }
        
        return adjusted
    }
    
    /**
     * 智能内容增强
     */
    private fun intelligentContentEnhancement(text: String): String {
        var enhanced = text
        
        // 智能标点符号处理
        enhanced = intelligentPunctuationHandling(enhanced)
        
        // 智能空白处理
        enhanced = intelligentWhitespaceHandling(enhanced)
        
        // 智能换行处理
        enhanced = intelligentLineBreakHandling(enhanced)
        
        return enhanced
    }
    
    /**
     * 渐进式矫正 - 分阶段应用不同强度的矫正
     */
    fun progressiveCorrection(text: String): String {
        if (text.isBlank()) return text
        
        // 检查缓存
        if (formatConfig.enableCaching) {
            val cacheKey = "progressive_${text.hashCode()}"
            progressiveCache[cacheKey]?.let { cached ->
                performanceMetrics.cacheHits++
                return cached
            }
            performanceMetrics.cacheMisses++
        }
        
        return performanceOptimizedProcessing(text, "progressiveCorrection") {
            var corrected = text
            
            // 第一阶段：基础清理
            corrected = basicCleanup(corrected)
            
            // 第二阶段：结构矫正
            corrected = structuralCorrection(corrected)
            
            // 第三阶段：语义优化
            corrected = semanticOptimization(corrected)
            
            // 第四阶段：最终润色
            corrected = finalPolishing(corrected)
            
            // 缓存结果
            if (formatConfig.enableCaching && text.length < 3000) {
                val cacheKey = "progressive_${text.hashCode()}"
                progressiveCache[cacheKey] = corrected
                cleanupCache()
            }
            
            corrected
        }
    }
    
    /**
     * 处理包含<think>标签的文本
     */
    fun processThinkTags(newText: String): Pair<String?, String?> {
        val buffer = thinkingBuffer.get()
        val fullText = buffer.toString() + newText
        
        var thinkingContent: String? = null
        var regularContent: String? = null
        
        val thinkStartPattern = "<think>"
        val thinkEndPattern = "</think>"
        
        val thinkStartIndex = fullText.indexOf(thinkStartPattern)
        val thinkEndIndex = fullText.indexOf(thinkEndPattern)
        
        when {
            // 找到完整的<think>...</think>
            thinkStartIndex != -1 && thinkEndIndex != -1 && thinkEndIndex > thinkStartIndex -> {
                val thinkContent = fullText.substring(
                    thinkStartIndex + thinkStartPattern.length,
                    thinkEndIndex
                )
                thinkingContent = thinkContent
                
                val afterThinkIndex = thinkEndIndex + thinkEndPattern.length
                if (afterThinkIndex < fullText.length) {
                    regularContent = fullText.substring(afterThinkIndex)
                }
                
                thinkingBuffer.set(StringBuilder())
                hasFoundThinkTag.set(true)
                isInsideThinkTag.set(false)
            }
            // 找到<think>但还没找到</think>
            thinkStartIndex != -1 && thinkEndIndex == -1 -> {
                isInsideThinkTag.set(true)
                val thinkingStartIndex = thinkStartIndex + thinkStartPattern.length
                if (thinkingStartIndex < fullText.length) {
                    thinkingContent = fullText.substring(thinkingStartIndex)
                }
                thinkingBuffer.set(StringBuilder(fullText))
            }
            // 没有找到<think>标签
            thinkStartIndex == -1 -> {
                if (isInsideThinkTag.get()) {
                    thinkingContent = newText
                    thinkingBuffer.set(StringBuilder(fullText))
                } else if (hasFoundThinkTag.get()) {
                    regularContent = newText
                } else {
                    regularContent = newText
                }
            }
        }
        
        return Pair(thinkingContent, regularContent)
    }
    
    /**
     * 重置<think>标签状态
     */
    fun resetThinkTagState() {
        thinkingBuffer.set(StringBuilder())
        isInsideThinkTag.set(false)
        hasFoundThinkTag.set(false)
    }
    
    // ========== 内容类型检测 ==========
    
    private enum class ContentType {
        CODE, MATH, LIST, TABLE, MIXED, PLAIN
    }
    
    private fun detectContentType(text: String): ContentType {
        val codeIndicators = listOf("```", "def ", "function ", "class ", "import ", "from ")
        val mathIndicators = listOf("$$", "$", "\\(", "\\[", "\\frac", "\\sum")
        val listIndicators = listOf("- ", "* ", "+ ", "1. ", "2. ")
        val tableIndicators = listOf("|", "---")
        
        val hasCode = codeIndicators.any { text.contains(it) }
        val hasMath = mathIndicators.any { text.contains(it) }
        val hasList = listIndicators.any { text.contains(it) }
        val hasTable = tableIndicators.any { text.contains(it) }
        
        val typeCount = listOf(hasCode, hasMath, hasList, hasTable).count { it }
        
        return when {
            typeCount > 1 -> ContentType.MIXED
            hasCode -> ContentType.CODE
            hasMath -> ContentType.MATH
            hasList -> ContentType.LIST
            hasTable -> ContentType.TABLE
            else -> ContentType.PLAIN
        }
    }
    
    // ========== 内容类型特定处理 ==========
    
    private fun processCodeContent(text: String): String {
        var processed = text
        processed = smartCodeDetectionAndFix(processed)
        processed = optimizeCodeBlockStructure(processed)
        return processed
    }
    
    private fun processMathContent(text: String): String {
        var processed = text
        // 修复LaTeX数学公式
        processed = processed.replace(Regex("\\$([^$]+)(?!\\$)"), "$$1$")
        processed = processed.replace(Regex("\\$\\$([^$]+)(?!\\$\\$)"), "$$$1$$")
        return processed
    }
    
    private fun processListContent(text: String): String {
        return optimizeListStructure(text)
    }
    
    private fun processTableContent(text: String): String {
        var processed = text
        // 修复表格格式
        processed = processed.replace(Regex("\\|\\s*([^|]+)\\s*\\|"), "| $1 |")
        processed = processed.replace(Regex("([^\\n])\n(\\|.+\\|)"), "$1\n\n$2")
        return processed
    }
    
    private fun processMixedContent(text: String): String {
        var processed = text
        processed = processCodeContent(processed)
        processed = processMathContent(processed)
        processed = processListContent(processed)
        processed = processTableContent(processed)
        return processed
    }
    
    private fun processPlainContent(text: String): String {
        return optimizeParagraphStructure(text)
    }
    
    // ========== 结构优化方法 ==========
    
    private fun optimizeParagraphStructure(text: String): String {
        var optimized = text
        
        // 修复段落间距
        optimized = optimized.replace(Regex("([.!?])\\s*\n([A-Z\\u4e00-\\u9fa5])")) { match ->
            "${match.groupValues[1]}\n\n${match.groupValues[2]}"
        }
        
        // 合并过短的段落
        optimized = optimized.replace(Regex("([^\\n]{1,20})\n\n([^\\n]{1,20})"), "$1 $2")
        
        return optimized
    }
    
    private fun optimizeListStructure(text: String): String {
        var optimized = text
        
        // 统一列表标记
        optimized = optimized.replace(Regex("^\\s*[\\*\\+]\\s+"), "- ")
        optimized = optimized.replace(Regex("\n\\s*[\\*\\+]\\s+"), "\n- ")
        
        // 修复有序列表编号
        optimized = renumberOrderedLists(optimized)
        
        return optimized
    }
    
    private fun optimizeCodeBlockStructure(text: String): String {
        var optimized = text
        
        // 确保代码块前后有换行
        optimized = optimized.replace(Regex("([^\\n])```"), "$1\n```")
        optimized = optimized.replace(Regex("```([^\\n])"), "```\n$1")
        
        // 修复未闭合的代码块
        val codeBlockCount = optimized.count { it == '`' }
        if (codeBlockCount % 6 == 3) {
            optimized += "\n```"
        }
        
        return optimized
    }
    
    // ========== 智能检测和修复 ==========
    
    private fun smartCodeDetectionAndFix(text: String): String {
        var fixed = text
        
        // 检测并包装代码块
        fixed = detectAndWrapCodeBlocks(fixed)
        
        // 修复代码块错误
        fixed = fixCodeBlockErrors(fixed)
        
        // 修复代码缩进
        fixed = fixCodeIndentation(fixed)
        
        return fixed
    }
    
    private fun detectAndWrapCodeBlocks(text: String): String {
        var wrapped = text
        
        // 检测常见编程语言关键词
        val codePatterns = mapOf(
            "python" to listOf("def ", "class ", "import ", "from ", "if __name__"),
            "javascript" to listOf("function ", "const ", "let ", "var ", "=>"),
            "java" to listOf("public class", "private ", "public ", "import java"),
            "kotlin" to listOf("fun ", "class ", "val ", "var ", "import kotlin")
        )
        
        for ((language, patterns) in codePatterns) {
            for (pattern in patterns) {
                if (wrapped.contains(pattern) && !wrapped.contains("```")) {
                    // 如果包含代码模式但没有代码块标记，添加代码块
                    wrapped = "```$language\n$wrapped\n```"
                    break
                }
            }
        }
        
        return wrapped
    }
    
    private fun fixCodeBlockErrors(text: String): String {
        var fixed = text
        
        // 修复语言标记
        fixed = fixed.replace(Regex("```(py|python3)"), "```python")
        fixed = fixed.replace(Regex("```(js|javascript)"), "```javascript")
        fixed = fixed.replace(Regex("```(kt|kotlin)"), "```kotlin")
        
        return fixed
    }
    
    private fun fixCodeIndentation(text: String): String {
        val lines = text.split("\n")
        val fixedLines = mutableListOf<String>()
        var inCodeBlock = false
        var codeLanguage = ""
        
        for (line in lines) {
            when {
                line.startsWith("```") -> {
                    if (!inCodeBlock) {
                        inCodeBlock = true
                        codeLanguage = line.removePrefix("```").trim()
                    } else {
                        inCodeBlock = false
                        codeLanguage = ""
                    }
                    fixedLines.add(line)
                }
                inCodeBlock -> {
                    // 在代码块内，根据语言修复缩进
                    val fixedLine = when (codeLanguage) {
                        "python" -> fixPythonIndentation(line)
                        "javascript", "java", "kotlin" -> fixBraceBasedIndentation(line)
                        else -> line
                    }
                    fixedLines.add(fixedLine)
                }
                else -> fixedLines.add(line)
            }
        }
        
        return fixedLines.joinToString("\n")
    }
    
    private fun fixPythonIndentation(line: String): String {
        // Python缩进修复逻辑
        val trimmed = line.trim()
        if (trimmed.isEmpty()) return ""
        
        // 计算应有的缩进级别
        val indentLevel = when {
            trimmed.startsWith("def ") || trimmed.startsWith("class ") -> 0
            trimmed.startsWith("if ") || trimmed.startsWith("for ") || 
            trimmed.startsWith("while ") || trimmed.startsWith("try:") -> 1
            trimmed.startsWith("else:") || trimmed.startsWith("elif ") ||
            trimmed.startsWith("except") || trimmed.startsWith("finally:") -> 1
            else -> 1
        }
        
        return "    ".repeat(indentLevel) + trimmed
    }
    
    private fun fixBraceBasedIndentation(line: String): String {
        // 基于大括号的语言缩进修复
        val trimmed = line.trim()
        if (trimmed.isEmpty()) return ""
        
        val indentLevel = when {
            trimmed.startsWith("public ") || trimmed.startsWith("private ") ||
            trimmed.startsWith("function ") -> 0
            trimmed.startsWith("}") -> 0
            else -> 1
        }
        
        return "    ".repeat(indentLevel) + trimmed
    }
    
    // ========== 渐进式矫正阶段 ==========
    
    private fun basicCleanup(text: String): String {
        var cleaned = text
        
        // 移除多余空白
        cleaned = cleaned.replace(Regex("[ \t]+\n"), "\n")
        cleaned = cleaned.replace(Regex("\n{4,}"), "\n\n\n")
        cleaned = cleaned.trim()
        
        return cleaned
    }
    
    private fun structuralCorrection(text: String): String {
        var corrected = text
        
        // 修复基本结构
        corrected = optimizeParagraphStructure(corrected)
        corrected = optimizeListStructure(corrected)
        corrected = optimizeCodeBlockStructure(corrected)
        
        return corrected
    }
    
    private fun semanticOptimization(text: String): String {
        var optimized = text
        
        // 语义级别的优化
        optimized = intelligentPunctuationHandling(optimized)
        optimized = intelligentWhitespaceHandling(optimized)
        
        return optimized
    }
    
    private fun finalPolishing(text: String): String {
        var polished = text
        
        // 最终润色
        polished = intelligentLineBreakHandling(polished)
        polished = basicCleanup(polished)
        
        return polished
    }
    
    // ========== 智能处理方法 ==========
    
    private fun intelligentPunctuationHandling(text: String): String {
        var handled = text
        
        // 修复标点符号间距
        handled = handled.replace(Regex("([.!?])([A-Z])"), "$1 $2")
        handled = handled.replace(Regex("([。！？])([\\u4e00-\\u9fa5])"), "$1$2")
        
        // 修复括号间距
        handled = handled.replace(Regex("\\(\\s+"), "(")
        handled = handled.replace(Regex("\\s+\\)"), ")")
        
        return handled
    }
    
    private fun intelligentWhitespaceHandling(text: String): String {
        var handled = text
        
        // 智能处理空白字符
        handled = handled.replace(Regex("[ \t]+"), " ")
        handled = handled.replace(Regex(" +\n"), "\n")
        handled = handled.replace(Regex("\n +"), "\n")
        
        return handled
    }
    
    private fun intelligentLineBreakHandling(text: String): String {
        var handled = text
        
        // 智能处理换行
        handled = handled.replace(Regex("([^\\n])\\n([A-Z\\u4e00-\\u9fa5])")) { match ->
            val before = match.groupValues[1]
            val after = match.groupValues[2]
            if (before.endsWith(".") || before.endsWith("。") || 
                before.endsWith("!") || before.endsWith("！") ||
                before.endsWith("?") || before.endsWith("？")) {
                "$before\n\n$after"
            } else {
                "$before $after"
            }
        }
        
        return handled
    }
    
    // ========== 格式调整方法 ==========
    
    private fun lightweightFormatting(text: String): String {
        return basicCleanup(text)
    }
    
    private fun standardFormatting(text: String): String {
        var formatted = text
        formatted = basicCleanup(formatted)
        formatted = structuralCorrection(formatted)
        return formatted
    }
    
    private fun heavyweightFormatting(text: String): String {
        var formatted = text
        formatted = basicCleanup(formatted)
        formatted = structuralCorrection(formatted)
        formatted = semanticOptimization(formatted)
        formatted = finalPolishing(formatted)
        return formatted
    }
    
    // ========== 辅助方法 ==========
    
    private fun renumberOrderedLists(text: String): String {
        val lines = text.split('\n')
        val result = mutableListOf<String>()
        val indentCounters = mutableMapOf<Int, Int>()
        
        for (line in lines) {
            val trimmedLine = line.trim()
            val orderedListMatch = Regex("^(\\d+)\\.\\s+(.*)").find(trimmedLine)
            
            if (orderedListMatch != null) {
                val (_, content) = orderedListMatch.destructured
                val currentIndent = line.length - line.trimStart().length
                
                if (!indentCounters.containsKey(currentIndent)) {
                    indentCounters[currentIndent] = 1
                } else {
                    indentCounters[currentIndent] = indentCounters[currentIndent]!! + 1
                }
                
                val keysToRemove = indentCounters.keys.filter { it > currentIndent }
                keysToRemove.forEach { indentCounters.remove(it) }
                
                result.add(" ".repeat(currentIndent) + "${indentCounters[currentIndent]}. $content")
            } else {
                if (trimmedLine.isNotEmpty() && !trimmedLine.matches(Regex("^[*+-]\\s+.*"))) {
                    indentCounters.clear()
                }
                result.add(line)
            }
        }
        
        return result.joinToString("\n")
    }
    
    private inline fun <T> performanceOptimizedProcessing(
        text: String,
        operation: String,
        processor: () -> T
    ): T {
        if (!formatConfig.enablePerformanceOptimization) {
            return processor()
        }
        
        val startTime = System.currentTimeMillis()
        
        try {
            val result = processor()
            
            val processingTime = System.currentTimeMillis() - startTime
            updatePerformanceMetrics(processingTime)
            
            if (processingTime > formatConfig.maxProcessingTimeMs) {
                logger.warn("$operation took ${processingTime}ms, exceeding threshold for text length: ${text.length}")
            }
            
            return result
        } catch (e: Exception) {
            logger.error("Error in $operation", e)
            throw e
        }
    }
    
    private fun updatePerformanceMetrics(processingTime: Long) {
        performanceMetrics.apply {
            processedChunks++
            totalProcessingTime += processingTime
            averageProcessingTime = totalProcessingTime / processedChunks
            if (processingTime > maxProcessingTime) {
                maxProcessingTime = processingTime
            }
        }
    }
    
    private fun cleanupCache() {
        if (formatConfig.enableCaching) {
            if (preprocessingCache.size > formatConfig.maxCacheSize) {
                val toRemove = preprocessingCache.size - formatConfig.maxCacheSize / 2
                preprocessingCache.keys.take(toRemove).forEach { preprocessingCache.remove(it) }
            }
            if (progressiveCache.size > formatConfig.maxCacheSize) {
                val toRemove = progressiveCache.size - formatConfig.maxCacheSize / 2
                progressiveCache.keys.take(toRemove).forEach { progressiveCache.remove(it) }
            }
        }
    }
}