package com.example.everytalk.util

/**
 * 性能监控数据
 */
data class PerformanceMetrics(
    var totalProcessingTime: Long = 0,
    var averageProcessingTime: Long = 0,
    var maxProcessingTime: Long = 0,
    var processedChunks: Int = 0,
    var cacheHits: Int = 0,
    var cacheMisses: Int = 0,
    var skippedProcessing: Int = 0
) {
    /**
     * 计算缓存命中率
     */
    fun getCacheHitRate(): Double {
        val totalCacheAccess = cacheHits + cacheMisses
        return if (totalCacheAccess > 0) {
            (cacheHits.toDouble() / totalCacheAccess) * 100
        } else {
            0.0
        }
    }
    
    /**
     * 计算跳过处理率
     */
    fun getSkipRate(): Double {
        val totalProcessingAttempts = processedChunks + skippedProcessing
        return if (totalProcessingAttempts > 0) {
            (skippedProcessing.toDouble() / totalProcessingAttempts) * 100
        } else {
            0.0
        }
    }
    
    /**
     * 重置所有指标
     */
    fun reset() {
        totalProcessingTime = 0
        averageProcessingTime = 0
        maxProcessingTime = 0
        processedChunks = 0
        cacheHits = 0
        cacheMisses = 0
        skippedProcessing = 0
    }
    
    /**
     * 生成性能摘要
     */
    fun getSummary(): String {
        return """
            Performance Metrics Summary:
            - Total Processing Time: ${totalProcessingTime}ms
            - Average Processing Time: ${averageProcessingTime}ms
            - Max Processing Time: ${maxProcessingTime}ms
            - Processed Chunks: $processedChunks
            - Cache Hit Rate: ${"%.2f".format(getCacheHitRate())}%
            - Skip Rate: ${"%.2f".format(getSkipRate())}%
        """.trimIndent()
    }
}