kotlin version: 2.0.21
error message: org.jetbrains.kotlin.utils.exceptions.KotlinIllegalArgumentExceptionWithAttachments: Exception was thrown during transformation of class org.jetbrains.kotlin.fir.declarations.impl.FirPrimaryConstructor
	at org.jetbrains.kotlin.fir.backend.generators.Fir2IrCallableDeclarationsGenerator.createIrConstructor(Fir2IrCallableDeclarationsGenerator.kt:1108)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.createAndCacheIrConstructor(Fir2IrDeclarationStorage.kt:461)
	at org.jetbrains.kotlin.fir.backend.Fir2IrDeclarationStorage.createAndCacheIrConstructor$default(Fir2IrDeclarationStorage.kt:454)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processClassMembers$fir2ir(Fir2IrConverter.kt:223)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processMemberDeclaration(Fir2IrConverter.kt:452)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processClassMembers$fir2ir(Fir2IrConverter.kt:230)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processMemberDeclaration(Fir2IrConverter.kt:452)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processClassMembers$fir2ir(Fir2IrConverter.kt:230)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processMemberDeclaration(Fir2IrConverter.kt:452)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.processFileAndClassMembers(Fir2IrConverter.kt:187)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.runSourcesConversion(Fir2IrConverter.kt:89)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter.access$runSourcesConversion(Fir2IrConverter.kt:63)
	at org.jetbrains.kotlin.fir.backend.Fir2IrConverter$Companion.generateIrModuleFragment(Fir2IrConverter.kt:664)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.runFir2IrConversion(convertToIr.kt:163)
	at org.jetbrains.kotlin.fir.pipeline.Fir2IrPipeline.convertToIrAndActualize(convertToIr.kt:129)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize(convertToIr.kt:99)
	at org.jetbrains.kotlin.fir.pipeline.ConvertToIrKt.convertToIrAndActualize$default(convertToIr.kt:72)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertToIrAndActualizeForJvm(jvmCompilerPipeline.kt:196)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.convertAnalyzedFirToIr(jvmCompilerPipeline.kt:169)
	at org.jetbrains.kotlin.cli.jvm.compiler.pipeline.JvmCompilerPipelineKt.compileModulesUsingFrontendIrAndLightTree(jvmCompilerPipeline.kt:140)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:148)
	at org.jetbrains.kotlin.cli.jvm.K2JVMCompiler.doExecute(K2JVMCompiler.kt:43)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:103)
	at org.jetbrains.kotlin.cli.common.CLICompiler.execImpl(CLICompiler.kt:49)
	at org.jetbrains.kotlin.cli.common.CLITool.exec(CLITool.kt:101)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:464)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.runCompiler(IncrementalJvmCompilerRunner.kt:73)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:506)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:675)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1660)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:714)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:598)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:844)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:721)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:400)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:720)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1570)
Caused by: java.lang.IllegalStateException: IrConstructorSymbolImpl is already bound. Signature: null. Owner: CONSTRUCTOR GENERATED[org.jetbrains.kotlinx.serialization.compiler.fir.SerializationPluginKey] visibility:private <> () returnType:EveryTalk.ui.screens.viewmodel.ApiHandler.BackendErrorContent.Companion [primary]
	at org.jetbrains.kotlin.ir.symbols.impl.IrSymbolBase.bind(IrSymbolImpl.kt:67)
	at org.jetbrains.kotlin.ir.declarations.impl.IrConstructorImpl.<init>(IrConstructorImpl.kt:62)
	at org.jetbrains.kotlin.ir.declarations.IrFactory.createConstructor(IrFactory.kt:104)
	at org.jetbrains.kotlin.ir.declarations.IrFactory.createConstructor$default(IrFactory.kt:90)
	at org.jetbrains.kotlin.fir.backend.generators.Fir2IrCallableDeclarationsGenerator.createIrConstructor(Fir2IrCallableDeclarationsGenerator.kt:190)
	... 49 more


