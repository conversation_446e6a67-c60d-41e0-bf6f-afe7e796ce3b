package com.example.everytalk.ui.theme

import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

object ChatDimensions {
    // 气泡相关
    const val BUBBLE_WIDTH_RATIO = 0.85f
    val CORNER_RADIUS_LARGE = 18.dp
    val CORNER_RADIUS_SMALL = 8.dp
    
    // 间距
    val HORIZONTAL_PADDING = 8.dp
    val VERTICAL_PADDING = 8.dp
    val BUBBLE_INNER_PADDING_HORIZONTAL = 12.dp
    val BUBBLE_INNER_PADDING_VERTICAL = 8.dp
    val CODE_BLOCK_PADDING_TOP = 12.dp
    val CODE_BLOCK_PADDING_HORIZONTAL = 16.dp
    val CODE_BLOCK_PADDING_BOTTOM = 4.dp
    val TABLE_CELL_PADDING = 12.dp
    val IMAGE_PADDING_VERTICAL = 4.dp
    val LOADING_SPACER_WIDTH = 8.dp
    
    // 尺寸
    val LOADING_INDICATOR_SIZE = 12.dp
    val LOADING_INDICATOR_STROKE_WIDTH = 1.5.dp
    val COPY_ICON_SIZE = 20.dp
    val TABLE_BORDER_WIDTH = 1.dp
    val TABLE_CORNER_RADIUS = 12.dp
    val CODE_BLOCK_CORNER_RADIUS = 16.dp
    
    // 字体
    val CODE_FONT_SIZE = 13.sp
    val CODE_LINE_HEIGHT = 19.sp
    val HEADER_1_SIZE = 28.sp
    val HEADER_2_SIZE = 24.sp
    val HEADER_DEFAULT_SIZE = 20.sp
}