package com.example.everytalk.util

import java.util.concurrent.ConcurrentHashMap

/**
 * 文本格式矫正器
 * 负责各种文本格式的智能矫正和优化
 */
class TextFormatCorrector(
    private var formatConfig: FormatCorrectionConfig,
    private val performanceMetrics: PerformanceMetrics
) {
    private val logger = AppLogger.forComponent("TextFormatCorrector")
    
    // 缓存系统
    private val correctionCache = ConcurrentHashMap<String, String>()
    private val languageDetectionCache = ConcurrentHashMap<String, String>()
    private val structureCache = ConcurrentHashMap<String, String>()
    
    /**
     * 更新格式矫正配置
     */
    fun updateConfig(config: FormatCorrectionConfig) {
        this.formatConfig = config
    }
    
    /**
     * 强大的AI输出格式矫正系统
     * 即使AI输出格式错误，也要尽力矫正为正确格式
     * 包含性能优化和缓存机制
     */
    fun enhancedFormatCorrection(text: String): String {
        if (text.isBlank()) return ""
        
        // 检查是否启用性能优化
        if (!formatConfig.enablePerformanceOptimization) {
            return applyFormatCorrections(text)
        }
        
        // 检查缓存
        if (formatConfig.enableCaching) {
            val cacheKey = text.hashCode().toString()
            correctionCache[cacheKey]?.let { cached ->
                performanceMetrics.cacheHits++
                return cached
            }
            performanceMetrics.cacheMisses++
        }
        
        // 如果文本太长，考虑分块处理
        if (formatConfig.enableAsyncProcessing && text.length > formatConfig.chunkSizeThreshold) {
            return performanceOptimizedProcessing(text, "enhancedFormatCorrection-chunked") {
                processTextInChunks(text)
            }
        }
        
        // 常规处理
        return performanceOptimizedProcessing(text, "enhancedFormatCorrection") {
            val result = applyFormatCorrections(text)
            
            // 缓存结果
            if (formatConfig.enableCaching && text.length < 10000) { // 只缓存较小的文本
                val cacheKey = text.hashCode().toString()
                correctionCache[cacheKey] = result
                cleanupCache()
            }
            
            result
        }
    }
    
    /**
     * 应用格式矫正
     */
    private fun applyFormatCorrections(text: String): String {
        var corrected = text
        
        // 根据配置应用不同的矫正功能
        if (formatConfig.enableCodeBlockCorrection) {
            corrected = fixCodeBlockFormat(corrected)
        }
        
        if (formatConfig.enableMarkdownCorrection) {
            corrected = fixMarkdownHeaders(corrected)
        }
        
        if (formatConfig.enableListCorrection) {
            corrected = fixListFormat(corrected)
        }
        
        if (formatConfig.enableLinkCorrection) {
            corrected = fixLinkFormat(corrected)
        }
        
        if (formatConfig.enableTableCorrection) {
            corrected = fixTableFormat(corrected)
        }
        
        if (formatConfig.enableQuoteCorrection) {
            corrected = fixQuoteFormat(corrected)
        }
        
        if (formatConfig.enableTextStyleCorrection) {
            corrected = fixTextStyleFormat(corrected)
        }
        
        if (formatConfig.enableParagraphCorrection) {
            corrected = fixParagraphFormat(corrected)
        }
        
        // 最后清理多余空白
        corrected = cleanExcessiveWhitespace(corrected)
        
        return corrected
    }
    
    /**
     * 修复代码块格式
     */
    private fun fixCodeBlockFormat(text: String): String {
        var fixed = text
        
        // 修复代码块标记
        fixed = fixed.replace(Regex("```\\s*([a-zA-Z]+)\\s*\n"), "```$1\n")
        fixed = fixed.replace(Regex("```\\s*\n"), "```\n")
        
        // 确保代码块前后有换行
        fixed = fixed.replace(Regex("([^\\n])```"), "$1\n```")
        fixed = fixed.replace(Regex("```([^\\n])"), "```\n$1")
        
        // 修复未闭合的代码块
        val codeBlockCount = fixed.count { it == '`' }
        if (codeBlockCount % 6 == 3) { // 奇数个```
            fixed += "\n```"
        }
        
        return fixed
    }
    
    /**
     * 修复Markdown标题格式
     */
    private fun fixMarkdownHeaders(text: String): String {
        var fixed = text
        
        // 修复标题格式（确保#后有空格）
        fixed = fixed.replace(Regex("^(#{1,6})([^\\s#])"), "$1 $2")
        fixed = fixed.replace(Regex("\n(#{1,6})([^\\s#])"), "\n$1 $2")
        
        // 修复标题前后的空行
        fixed = fixed.replace(Regex("([^\\n])\n(#{1,6}\\s)"), "$1\n\n$2")
        fixed = fixed.replace(Regex("(#{1,6}\\s[^\\n]+)\n([^\\n#])"), "$1\n\n$2")
        
        return fixed
    }
    
    /**
     * 修复列表格式
     */
    private fun fixListFormat(text: String): String {
        var fixed = text
        
        // 统一无序列表标记为 -
        fixed = fixed.replace(Regex("^\\s*[\\*\\+]\\s+"), "- ")
        fixed = fixed.replace(Regex("\n\\s*[\\*\\+]\\s+"), "\n- ")
        
        // 修复有序列表格式
        fixed = fixed.replace(Regex("^\\s*(\\d+)[.):]\\s*"), "$1. ")
        fixed = fixed.replace(Regex("\n\\s*(\\d+)[.):]\\s*"), "\n$1. ")
        
        // 确保列表项之间的间距
        fixed = fixed.replace(Regex("(- .+)\n(- )"), "$1\n\n$2")
        
        return fixed
    }
    
    /**
     * 修复链接格式
     */
    private fun fixLinkFormat(text: String): String {
        var fixed = text
        
        // 修复Markdown链接格式
        fixed = fixed.replace(Regex("\\[([^\\]]+)\\]\\s*\\(([^)]+)\\)"), "[$1]($2)")
        
        // 修复URL格式
        fixed = fixed.replace(Regex("(https?://[^\\s]+)"), "[$1]($1)")
        
        return fixed
    }
    
    /**
     * 修复表格格式
     */
    private fun fixTableFormat(text: String): String {
        var fixed = text
        
        // 修复表格行格式
        fixed = fixed.replace(Regex("\\|\\s*([^|]+)\\s*\\|"), "| $1 |")
        
        // 确保表格前后有空行
        fixed = fixed.replace(Regex("([^\\n])\n(\\|.+\\|)"), "$1\n\n$2")
        fixed = fixed.replace(Regex("(\\|.+\\|)\n([^\\|\\n])"), "$1\n\n$2")
        
        return fixed
    }
    
    /**
     * 修复引用格式
     */
    private fun fixQuoteFormat(text: String): String {
        var fixed = text
        
        // 修复引用块格式
        fixed = fixed.replace(Regex("^>\\s*(.+)"), "> $1")
        fixed = fixed.replace(Regex("\n>\\s*(.+)"), "\n> $1")
        
        return fixed
    }
    
    /**
     * 修复文本样式格式
     */
    private fun fixTextStyleFormat(text: String): String {
        var fixed = text
        
        // 修复粗体格式
        fixed = fixed.replace(Regex("\\*\\*([^*]+)\\*\\*"), "**$1**")
        
        // 修复斜体格式
        fixed = fixed.replace(Regex("\\*([^*]+)\\*"), "*$1*")
        
        // 修复行内代码格式
        fixed = fixed.replace(Regex("`([^`]+)`"), "`$1`")
        
        return fixed
    }
    
    /**
     * 修复段落格式
     */
    private fun fixParagraphFormat(text: String): String {
        var fixed = text
        
        // 确保段落之间有适当间距
        fixed = fixed.replace(Regex("([.!?])\\s*\n([A-Z\\u4e00-\\u9fa5])")) { match ->
            val punctuation = match.groupValues[1]
            val nextSentence = match.groupValues[2]
            "$punctuation\n\n$nextSentence"
        }
        
        return fixed
    }
    
    /**
     * 清理多余空白
     */
    fun cleanExcessiveWhitespace(text: String): String {
        var cleaned = text
        
        // 移除多余的空行（保留最多两个连续换行）
        cleaned = cleaned.replace(Regex("\n{4,}"), "\n\n\n")
        
        // 移除行尾空白
        cleaned = cleaned.replace(Regex("[ \t]+\n"), "\n")
        
        // 移除文档开头和结尾的空白
        cleaned = cleaned.trim()
        
        return cleaned
    }
    
    /**
     * 分块处理大文本
     */
    private fun processTextInChunks(text: String): String {
        val chunkSize = formatConfig.chunkSizeThreshold
        val chunks = text.chunked(chunkSize)
        
        return chunks.joinToString("") { chunk ->
            applyFormatCorrections(chunk)
        }
    }
    
    /**
     * 性能优化的文本处理包装器
     */
    private inline fun <T> performanceOptimizedProcessing(
        text: String,
        operation: String,
        processor: () -> T
    ): T {
        if (!formatConfig.enablePerformanceOptimization) {
            return processor()
        }
        
        val startTime = System.currentTimeMillis()
        
        try {
            val result = processor()
            
            // 更新性能指标
            val processingTime = System.currentTimeMillis() - startTime
            updatePerformanceMetrics(processingTime)
            
            // 如果处理时间超过阈值，记录警告
            if (processingTime > formatConfig.maxProcessingTimeMs) {
                logger.warn("$operation took ${processingTime}ms, exceeding threshold of ${formatConfig.maxProcessingTimeMs}ms for text length: ${text.length}")
            }
            
            return result
        } catch (e: Exception) {
            logger.error("Error in $operation", e)
            throw e
        }
    }
    
    /**
     * 更新性能指标
     */
    private fun updatePerformanceMetrics(processingTime: Long) {
        performanceMetrics.apply {
            processedChunks++
            totalProcessingTime += processingTime
            averageProcessingTime = totalProcessingTime / processedChunks
            if (processingTime > maxProcessingTime) {
                maxProcessingTime = processingTime
            }
        }
    }
    
    /**
     * 清理缓存
     */
    private fun cleanupCache() {
        if (formatConfig.enableCaching) {
            // 如果缓存超过最大大小，清理最旧的条目
            if (correctionCache.size > formatConfig.maxCacheSize) {
                val toRemove = correctionCache.size - formatConfig.maxCacheSize / 2
                correctionCache.keys.take(toRemove).forEach { correctionCache.remove(it) }
            }
            if (languageDetectionCache.size > formatConfig.maxCacheSize) {
                val toRemove = languageDetectionCache.size - formatConfig.maxCacheSize / 2
                languageDetectionCache.keys.take(toRemove).forEach { languageDetectionCache.remove(it) }
            }
            if (structureCache.size > formatConfig.maxCacheSize) {
                val toRemove = structureCache.size - formatConfig.maxCacheSize / 2
                structureCache.keys.take(toRemove).forEach { structureCache.remove(it) }
            }
        }
    }
}