package com.example.everytalk.util

import com.example.everytalk.models.*
import com.example.everytalk.data.DataClass.Message
import com.example.everytalk.data.DataClass.Sender
import com.example.everytalk.data.DataClass.WebSearchResult
import com.example.everytalk.data.DataClass.SimpleTextApiMessage
import com.example.everytalk.data.DataClass.PartsApiMessage
import com.example.everytalk.data.DataClass.AbstractApiMessage
import com.example.everytalk.data.DataClass.ApiContentPart
import com.example.everytalk.data.DataClass.toRole
import com.example.everytalk.data.network.AppStreamEvent
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import java.util.*
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.atomic.AtomicReference

/**
 * 消息处理器 - 重构版本
 * 负责协调各个组件进行消息处理
 */
class MessageProcessor {
    private val logger = AppLogger.forComponent("MessageProcessor")
    
    // 组件实例
    private var formatConfig = FormatCorrectionConfig()
    private val performanceMetrics = PerformanceMetrics()
    private val textFormatCorrector = TextFormatCorrector(formatConfig, performanceMetrics)
    private val intelligentTextProcessor = IntelligentTextProcessor(formatConfig, performanceMetrics)
    
    // 处理状态
    private val isCancelled = AtomicBoolean(false)
    private val currentTextBuilder = AtomicReference(StringBuilder())
    private val currentReasoningBuilder = AtomicReference(StringBuilder())
    private val processedChunks = ConcurrentHashMap<String, String>()
    private val messagesMutex = Mutex()
    
    /**
     * 更新格式矫正配置
     */
    fun updateFormatConfig(config: FormatCorrectionConfig) {
        this.formatConfig = config
        textFormatCorrector.updateConfig(config)
        intelligentTextProcessor.updateConfig(config)
    }
    
    /**
     * 获取性能指标
     */
    fun getPerformanceMetrics(): PerformanceMetrics = performanceMetrics
    
    /**
     * 重置性能指标
     */
    fun resetPerformanceMetrics() {
        performanceMetrics.reset()
    }
    
    /**
     * 检查是否应该跳过处理
     */
    private fun shouldSkipProcessing(text: String, operation: String): Boolean {
        if (!formatConfig.enablePerformanceOptimization) return false
        
        // 如果文本太短，跳过复杂处理
        if (text.length < 50 && formatConfig.enableProgressiveCorrection) {
            performanceMetrics.skippedProcessing++
            return true
        }
        
        // 如果是纯文本且很简单，跳过处理
        if (isPlainText(text) && text.length < 200) {
            performanceMetrics.skippedProcessing++
            return true
        }
        
        return false
    }
    
    /**
     * 检查文本是否为纯文本
     */
    private fun isPlainText(text: String): Boolean {
        val markdownIndicators = listOf("```", "**", "*", "#", "-", "|", ">", "[", "]", "(", ")")
        return markdownIndicators.none { text.contains(it) }
    }
    
    /**
     * 检查文本是否实际为空
     */
    private fun isEffectivelyEmpty(text: String): Boolean {
        return text.isBlank() || text.replace(Regex("[\\s\\n\\r\\t]"), "").isEmpty()
    }
    
    /**
     * 标准化文本
     */
    private fun normalizeText(text: String): String {
        return text.replace(Regex("\\s+"), " ").trim()
    }
    
    /**
     * 检查新文本是否只是空白字符或重复内容
     */
    private fun shouldSkipTextChunk(newText: String, existingText: String): Boolean {
        if (newText.isBlank()) return true
        
        val whitespaceOnly = newText.replace(Regex("[^\n \t]"), "")
        if (whitespaceOnly == newText && newText.length > 10) {
            return true
        }
        
        return false
    }
    
    /**
     * 处理流式事件
     */
    suspend fun processStreamEvent(
        event: AppStreamEvent,
        currentMessageId: String
    ): ProcessedEventResult {
        if (isCancelled.get()) {
            logger.debug("Event processing cancelled for message $currentMessageId")
            return ProcessedEventResult.Cancelled
        }
        
        return PerformanceMonitor.measure("MessageProcessor.processStreamEvent") {
            messagesMutex.withLock {
                try {
                    when (event) {
                        is AppStreamEvent.Text -> processTextEvent(event)
                        is AppStreamEvent.Content -> processContentEvent(event)
                        is AppStreamEvent.Reasoning -> processReasoningEvent(event)
                        is AppStreamEvent.StreamEnd, 
                        is AppStreamEvent.ToolCall, 
                        is AppStreamEvent.Finish -> {
                            cleanupAfterProcessing()
                            ProcessedEventResult.ReasoningComplete
                        }
                        is AppStreamEvent.WebSearchStatus -> {
                            ProcessedEventResult.StatusUpdate(event.stage)
                        }
                        is AppStreamEvent.WebSearchResults -> {
                            ProcessedEventResult.WebSearchResults(event.results)
                        }
                        is AppStreamEvent.Error -> {
                            ProcessedEventResult.Error("SSE Error: ${event.message}")
                        }
                    }
                } catch (e: Exception) {
                    logger.error("Error processing event", e)
                    ProcessedEventResult.Error("Error processing event: ${e.message}")
                }
            }
        }
    }
    
    /**
     * 处理文本事件
     */
    private fun processTextEvent(event: AppStreamEvent.Text): ProcessedEventResult {
        if (event.text.isEmpty() || isEffectivelyEmpty(event.text)) {
            return ProcessedEventResult.NoChange
        }
        
        if (shouldSkipTextChunk(event.text, currentTextBuilder.get().toString())) {
            return ProcessedEventResult.NoChange
        }
        
        val normalizedText = normalizeText(event.text)
        val chunkKey = "content_${normalizedText.hashCode()}"
        
        if (!processedChunks.containsKey(chunkKey)) {
            val processedText = processTextChunk(event.text)
            
            // 使用智能文本处理器处理<think>标签
            val (thinkingContent, regularContent) = intelligentTextProcessor.processThinkTags(processedText)
            
            // 处理思考内容
            thinkingContent?.let { thinking ->
                if (thinking.isNotEmpty() && !shouldSkipTextChunk(thinking, currentReasoningBuilder.get().toString())) {
                    currentReasoningBuilder.get().append(thinking)
                    processedChunks[chunkKey] = normalizedText
                    return ProcessedEventResult.ReasoningUpdated(currentReasoningBuilder.get().toString())
                }
            }
            
            // 处理正式内容
            regularContent?.let { regular ->
                if (regular.isNotEmpty() && !shouldSkipTextChunk(regular, currentTextBuilder.get().toString())) {
                    currentTextBuilder.get().append(regular)
                }
            }
            
            processedChunks[chunkKey] = normalizedText
        }
        
        return ProcessedEventResult.ContentUpdated(getCurrentText())
    }
    
    /**
     * 处理内容事件
     */
    private fun processContentEvent(event: AppStreamEvent.Content): ProcessedEventResult {
        if (event.text.isEmpty() || isEffectivelyEmpty(event.text)) {
            return ProcessedEventResult.NoChange
        }
        
        // 检查是否是Gemini最终清理事件
        if (event.text.startsWith("__GEMINI_FINAL_CLEANUP__\n")) {
            val cleanedContent = event.text.removePrefix("__GEMINI_FINAL_CLEANUP__\n")
            currentTextBuilder.set(StringBuilder(cleanedContent))
            processedChunks.clear()
            logger.debug("Applied Gemini final cleanup to message content")
            return ProcessedEventResult.ContentUpdated(getCurrentText())
        }
        
        if (shouldSkipTextChunk(event.text, currentTextBuilder.get().toString())) {
            return ProcessedEventResult.NoChange
        }
        
        val normalizedText = normalizeText(event.text)
        val chunkKey = "content_${normalizedText.hashCode()}"
        
        if (!processedChunks.containsKey(chunkKey)) {
            val processedText = processTextChunk(event.text)
            
            // 使用智能文本处理器处理<think>标签
            val (thinkingContent, regularContent) = intelligentTextProcessor.processThinkTags(processedText)
            
            // 处理思考内容
            thinkingContent?.let { thinking ->
                if (thinking.isNotEmpty() && !shouldSkipTextChunk(thinking, currentReasoningBuilder.get().toString())) {
                    currentReasoningBuilder.get().append(thinking)
                    processedChunks[chunkKey] = normalizedText
                    return ProcessedEventResult.ReasoningUpdated(currentReasoningBuilder.get().toString())
                }
            }
            
            // 处理正式内容
            regularContent?.let { regular ->
                if (regular.isNotEmpty() && !shouldSkipTextChunk(regular, currentTextBuilder.get().toString())) {
                    currentTextBuilder.get().append(regular)
                }
            }
            
            processedChunks[chunkKey] = normalizedText
        }
        
        return ProcessedEventResult.ContentUpdated(getCurrentText())
    }
    
    /**
     * 处理推理事件
     */
    private fun processReasoningEvent(event: AppStreamEvent.Reasoning): ProcessedEventResult {
        if (event.text.isEmpty() || isEffectivelyEmpty(event.text)) {
            return ProcessedEventResult.NoChange
        }
        
        val normalizedText = normalizeText(event.text)
        val chunkKey = "reasoning_${normalizedText.hashCode()}"
        
        if (!processedChunks.containsKey(chunkKey)) {
            val processedText = processTextChunk(event.text)
            currentReasoningBuilder.get().append(processedText)
            processedChunks[chunkKey] = normalizedText
        }
        
        return ProcessedEventResult.ReasoningUpdated(getCurrentReasoning() ?: "")
    }
    
    /**
     * 处理文本块
     */
    private fun processTextChunk(text: String): String {
        // 智能跳过检查
        if (shouldSkipProcessing(text, "realtimePreprocessing")) {
            return text
        }
        
        // 应用实时格式预处理
        var processed = realtimeFormatPreprocessing(text)
        
        // 应用超级智能预处理
        if (formatConfig.enableIntelligentFormatting) {
            processed = intelligentTextProcessor.superIntelligentPreprocessing(processed)
        }
        
        return processed
    }
    
    /**
     * 实时格式预处理
     */
    private fun realtimeFormatPreprocessing(text: String): String {
        if (text.isBlank() || !formatConfig.enableRealtimePreprocessing) return text
        
        // 快速跳过检查
        if (!formatConfig.enablePerformanceOptimization) {
            return applyRealtimeCorrections(text)
        }
        
        // 如果文本很短，跳过复杂处理
        if (text.length < 50 && formatConfig.enableProgressiveCorrection) {
            performanceMetrics.skippedProcessing++
            return text
        }
        
        return applyRealtimeCorrections(text)
    }
    
    /**
     * 应用实时矫正
     */
    private fun applyRealtimeCorrections(text: String): String {
        var preprocessed = text
        
        // 根据矫正强度应用不同级别的预处理
        when (formatConfig.correctionIntensity) {
            CorrectionIntensity.LIGHT -> {
                preprocessed = textFormatCorrector.cleanExcessiveWhitespace(preprocessed)
            }
            CorrectionIntensity.MODERATE -> {
                preprocessed = quickFormatFix(preprocessed)
                if (formatConfig.enableCodeBlockCorrection) {
                    preprocessed = preprocessCodeBlocks(preprocessed)
                }
                if (formatConfig.enableMarkdownCorrection) {
                    preprocessed = preprocessMarkdown(preprocessed)
                }
                preprocessed = textFormatCorrector.cleanExcessiveWhitespace(preprocessed)
            }
            CorrectionIntensity.AGGRESSIVE -> {
                preprocessed = quickFormatFix(preprocessed)
                if (formatConfig.enableCodeBlockCorrection) {
                    preprocessed = preprocessCodeBlocks(preprocessed)
                }
                if (formatConfig.enableMarkdownCorrection) {
                    preprocessed = preprocessMarkdown(preprocessed)
                }
                preprocessed = textFormatCorrector.enhancedFormatCorrection(preprocessed)
            }
        }
        
        return preprocessed
    }
    
    /**
     * 快速修复常见格式错误
     */
    private fun quickFormatFix(text: String): String {
        var fixed = text
        
        // 修复常见的标点符号问题
        fixed = fixed.replace(Regex("([.!?])([A-Z])"), "$1 $2")
        fixed = fixed.replace(Regex("([。！？])([\\u4e00-\\u9fa5])"), "$1$2")
        
        // 修复常见的括号问题
        fixed = fixed.replace(Regex("\\(\\s+"), "(")
        fixed = fixed.replace(Regex("\\s+\\)"), ")")
        
        // 修复常见的引号问题
        fixed = fixed.replace(Regex("\"\\s+"), "\"")
        fixed = fixed.replace(Regex("\\s+\""), "\"")
        
        return fixed
    }
    
    /**
     * 预处理代码块标记
     */
    private fun preprocessCodeBlocks(text: String): String {
        var processed = text
        
        // 确保代码块标记前后有换行
        processed = processed.replace(Regex("([^\\n])```"), "$1\n```")
        processed = processed.replace(Regex("```([^\\n])"), "```\n$1")
        
        // 修复常见的代码块语言标记错误
        processed = processed.replace(Regex("```(python|java|javascript|kotlin|swift|cpp|c\\+\\+)\\s*\n"), "```$1\n")
        
        return processed
    }
    
    /**
     * 预处理Markdown标记
     */
    private fun preprocessMarkdown(text: String): String {
        var processed = text
        
        // 确保标题标记格式正确
        processed = processed.replace(Regex("^(#{1,6})([^\\s#])"), "$1 $2")
        processed = processed.replace(Regex("\n(#{1,6})([^\\s#])"), "\n$1 $2")
        
        // 确保列表标记格式正确
        processed = processed.replace(Regex("^([\\-\\*\\+])([^\\s])"), "$1 $2")
        processed = processed.replace(Regex("\n([\\-\\*\\+])([^\\s])"), "\n$1 $2")
        
        return processed
    }
    
    /**
     * 处理后清理
     */
    private fun cleanupAfterProcessing() {
        // 清理缓存等资源
        if (formatConfig.enableCaching) {
            // 这里可以添加缓存清理逻辑
        }
    }
    
    /**
     * 取消消息处理
     */
    fun cancel() {
        isCancelled.set(true)
        logger.debug("Message processing cancelled")
    }
    
    /**
     * 重置处理器状态
     */
    fun reset() {
        isCancelled.set(false)
        currentTextBuilder.set(StringBuilder())
        currentReasoningBuilder.set(StringBuilder())
        processedChunks.clear()
        
        // 重置智能文本处理器的<think>标签状态
        intelligentTextProcessor.resetThinkTagState()
        
        logger.debug("Message processor reset")
    }
    
    /**
     * 获取当前文本内容
     */
    fun getCurrentText(): String {
        val rawText = currentTextBuilder.get().toString()
        
        // 智能跳过检查
        if (shouldSkipProcessing(rawText, "enhancedFormatCorrection")) {
            return textFormatCorrector.cleanExcessiveWhitespace(rawText)
        }
        
        // 使用渐进式矫正或完整矫正
        val corrected = if (formatConfig.enableProgressiveCorrection) {
            intelligentTextProcessor.progressiveCorrection(rawText)
        } else {
            textFormatCorrector.enhancedFormatCorrection(rawText)
        }
        
        // 应用超级智能预处理作为最终处理步骤
        return if (formatConfig.enableIntelligentFormatting) {
            intelligentTextProcessor.superIntelligentPreprocessing(corrected)
        } else {
            corrected
        }
    }
    
    /**
     * 获取当前推理内容
     */
    fun getCurrentReasoning(): String? {
        val reasoning = currentReasoningBuilder.get().toString()
        return if (reasoning.isBlank()) null else reasoning
    }
    
    /**
     * 将UI消息转换为API消息
     */
    fun convertToApiMessage(message: Message): AbstractApiMessage {
        return if (message.attachments.isNotEmpty()) {
            val parts = mutableListOf<ApiContentPart>()
            if (message.text.isNotBlank()) {
                parts.add(ApiContentPart.Text(message.text))
            }
            PartsApiMessage(
                id = message.id,
                role = message.sender.toRole(),
                parts = parts,
                name = message.name
            )
        } else {
            SimpleTextApiMessage(
                id = message.id,
                role = message.sender.toRole(),
                content = message.text,
                name = message.name
            )
        }
    }
    
    /**
     * 创建新的AI消息
     */
    fun createNewAiMessage(): Message {
        return Message(
            id = UUID.randomUUID().toString(),
            text = "",
            sender = Sender.AI,
            contentStarted = false
        )
    }
    
    /**
     * 创建新的用户消息
     */
    fun createNewUserMessage(
        text: String,
        imageUrls: List<String>? = null,
        attachments: List<com.example.everytalk.models.SelectedMediaItem>? = null
    ): Message {
        return Message(
            id = "user_${UUID.randomUUID()}",
            text = text,
            sender = Sender.User,
            timestamp = System.currentTimeMillis(),
            contentStarted = true,
            imageUrls = imageUrls?.ifEmpty { null },
            attachments = attachments ?: emptyList()
        )
    }
}

/**
 * 处理事件的结果
 */
sealed class ProcessedEventResult {
    data class ContentUpdated(val content: String) : ProcessedEventResult()
    data class ReasoningUpdated(val reasoning: String) : ProcessedEventResult()
    object ReasoningComplete : ProcessedEventResult()
    data class StatusUpdate(val stage: String) : ProcessedEventResult()
    data class WebSearchResults(val results: List<WebSearchResult>) : ProcessedEventResult()
    data class Error(val message: String) : ProcessedEventResult()
    object Cancelled : ProcessedEventResult()
    object NoChange : ProcessedEventResult()
}