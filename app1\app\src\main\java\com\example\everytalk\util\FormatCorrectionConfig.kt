package com.example.everytalk.util

/**
 * 格式矫正配置
 */
data class FormatCorrectionConfig(
    val enableRealtimePreprocessing: Boolean = true,
    val enableCodeBlockCorrection: Boolean = true,
    val enableMarkdownCorrection: Boolean = true,
    val enableListCorrection: Boolean = true,
    val enableLinkCorrection: Boolean = true,
    val enableTableCorrection: Boolean = true,
    val enableQuoteCorrection: Boolean = true,
    val enableTextStyleCorrection: Boolean = true,
    val enableParagraphCorrection: Boolean = true,
    val enableJsonCorrection: Boolean = true,
    val enableXmlHtmlCorrection: Boolean = true,
    val enableMathCorrection: Boolean = true,
    val enableProgrammingSyntaxCorrection: Boolean = true,
    // 新增的智能修复功能
    val enableSmartCodeDetection: Boolean = true,
    val enableAutoLanguageDetection: Boolean = true,
    val enableIntelligentFormatting: Boolean = true,
    val enableContextAwareCorrection: Boolean = true,
    val enableSemanticCorrection: Boolean = true,
    val enableOutputStructureOptimization: Boolean = true,
    val enableAdvancedErrorRecovery: Boolean = true,
    val correctionIntensity: CorrectionIntensity = CorrectionIntensity.MODERATE,
    // 性能优化配置
    val enablePerformanceOptimization: Boolean = true,
    val maxProcessingTimeMs: Long = 3, // 降低到3毫秒以提高响应速度
    val enableCaching: Boolean = true,
    val maxCacheSize: Int = 2000, // 增加缓存大小
    val enableAsyncProcessing: Boolean = true,
    val chunkSizeThreshold: Int = 300, // 降低阈值以更早启用分块处理
    val enableProgressiveCorrection: Boolean = true, // 渐进式矫正
    // 新增性能优化选项
    val enableLazyProcessing: Boolean = true, // 延迟处理
    val enableBatchProcessing: Boolean = true, // 批量处理
    val enableMemoryOptimization: Boolean = true, // 内存优化
    val enableStreamProcessing: Boolean = true // 流式处理
)

/**
 * 矫正强度级别
 */
enum class CorrectionIntensity {
    LIGHT,      // 轻度矫正：只修复明显错误
    MODERATE,   // 中度矫正：修复常见错误和格式问题
    AGGRESSIVE  // 激进矫正：尽可能修复所有可能的格式问题
}