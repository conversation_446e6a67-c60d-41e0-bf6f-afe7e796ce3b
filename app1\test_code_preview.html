<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试代码预览功能</title>
    <style>
        body {
            font-family: system-ui, -apple-system, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 16px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
            font-size: 2.5em;
        }
        .success-message {
            color: #4caf50;
            font-size: 1.2em;
            margin: 20px 0;
            padding: 15px;
            background: #e8f5e8;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
        }
        .feature-list {
            text-align: left;
            margin: 30px 0;
        }
        .feature-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #667eea;
        }
        .animation {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="animation">🎉 代码预览功能已修复！</h1>
        
        <div class="success-message">
            <strong>✅ 编译成功！</strong><br>
            CodePreview.kt 文件的语法错误已经完全修复
        </div>
        
        <div class="feature-list">
            <h3>支持的代码类型：</h3>
            <div class="feature-item">📄 HTML - 完整网页预览</div>
            <div class="feature-item">🎨 CSS - 样式效果预览</div>
            <div class="feature-item">⚡ JavaScript - 交互式代码执行</div>
            <div class="feature-item">🖼️ SVG - 矢量图形渲染</div>
            <div class="feature-item">📝 Markdown - 文档格式化</div>
            <div class="feature-item">📊 图表库 - D3.js, Chart.js, P5.js</div>
            <div class="feature-item">🧮 数学公式 - LaTeX/MathJax</div>
            <div class="feature-item">🎯 可视化 - Mermaid, PlantUML</div>
        </div>
        
        <p style="color: #666; margin-top: 30px;">
            现在您可以在应用中正常使用代码预览功能了！
        </p>
    </div>
    
    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            const features = document.querySelectorAll('.feature-item');
            features.forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '0';
                    item.style.transform = 'translateX(-20px)';
                    item.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateX(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>
