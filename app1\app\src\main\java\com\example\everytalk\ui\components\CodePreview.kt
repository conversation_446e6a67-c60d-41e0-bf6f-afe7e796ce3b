package com.example.everytalk.ui.components

import android.content.Intent
import android.net.Uri
import android.webkit.WebView
import android.webkit.WebViewClient
import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material.icons.filled.OpenInBrowser
import androidx.compose.material.icons.filled.Error
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.zIndex
import java.io.File
import androidx.core.content.FileProvider
import java.io.FileOutputStream

/**
 * 代码预览组件
 * 支持HTML、SVG、CSS等可视化代码的预览
 */
@Composable
fun CodePreviewButton(
    code: String,
    language: String?,
    modifier: Modifier = Modifier
) {
    var showPreview by remember { mutableStateOf(false) }
    
    // 判断是否支持预览
    val isPreviewable = isCodePreviewable(language, code)
    
    // 检测代码错误
    val codeErrors = detectCodeErrors(code, language)
    val hasErrors = codeErrors.isNotEmpty()
    
    if (isPreviewable) {
        Surface(
            onClick = { showPreview = true },
            modifier = modifier,
            shape = RoundedCornerShape(16.dp), // 更大的圆角
            color = Color(0xFF1E1E1E), // 始终使用正常背景色
            contentColor = Color(0xFFE0E0E0)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 12.dp),
                horizontalArrangement = Arrangement.Center,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Filled.Visibility, // 始终使用预览图标
                    contentDescription = "查看预览",
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = "查看预览", // 始终显示"查看预览"
                    style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Medium)
                )
            }
        }
        
        if (showPreview) {
            CodePreviewDialog(
                code = code,
                language = language,
                errors = codeErrors,
                onDismiss = { showPreview = false }
            )
        }
    }
}

/**
 * 代码预览对话框
 */
@Composable
private fun CodePreviewDialog(
    code: String,
    language: String?,
    errors: List<String>,
    onDismiss: () -> Unit
) {
    // 动画状态
    var visible by remember { mutableStateOf(false) }
    val context = LocalContext.current
    
    LaunchedEffect(Unit) {
        visible = true
    }
    
    Dialog(
        onDismissRequest = {
            visible = false
            onDismiss()
        },
        properties = DialogProperties(
            usePlatformDefaultWidth = false,
            dismissOnBackPress = true,
            dismissOnClickOutside = true
        )
    ) {
        AnimatedVisibility(
            visible = visible,
            enter = fadeIn(
                animationSpec = tween(300, easing = EaseOutCubic)
            ) + scaleIn(
                initialScale = 0.8f,
                animationSpec = tween(300, easing = EaseOutCubic)
            ),
            exit = fadeOut(
                animationSpec = tween(200, easing = EaseInCubic)
            ) + scaleOut(
                targetScale = 0.8f,
                animationSpec = tween(200, easing = EaseInCubic)
            )
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(8.dp), // 极窄的边距
                contentAlignment = Alignment.Center
            ) {
                Surface(
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(24.dp)), // 更大的外圆角
                    shape = RoundedCornerShape(24.dp),
                    color = Color.White, // 纯白背景
                    tonalElevation = 16.dp
                ) {
                    Column(
                        modifier = Modifier.fillMaxSize()
                    ) {
                        // 如果有错误，显示错误信息
                        if (errors.isNotEmpty()) {
                            Surface(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(8.dp)
                                    .clip(RoundedCornerShape(20.dp)),
                                color = Color(0xFFFFEBEE),
                                tonalElevation = 2.dp
                            ) {
                                Column(
                                    modifier = Modifier.padding(16.dp)
                                ) {
                                    Row(
                                        verticalAlignment = Alignment.CenterVertically
                                    ) {
                                        Icon(
                                            Icons.Filled.Error,
                                            contentDescription = "错误",
                                            tint = Color(0xFFD32F2F),
                                            modifier = Modifier.size(20.dp)
                                        )
                                        Spacer(modifier = Modifier.width(8.dp))
                                        Text(
                                            text = "代码错误",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = Color(0xFFD32F2F),
                                            fontWeight = FontWeight.Bold
                                        )
                                    }
                                    Spacer(modifier = Modifier.height(8.dp))
                                    errors.forEach { error ->
                                        Text(
                                            text = "• $error",
                                            style = MaterialTheme.typography.bodyMedium,
                                            color = Color(0xFF8B0000),
                                            modifier = Modifier.padding(vertical = 2.dp)
                                        )
                                    }
                                }
                            }
                        }
                        
                        // 预览内容
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .weight(1f)
                                .padding(horizontal = 8.dp)
                                .clip(RoundedCornerShape(20.dp)), // 内容区域圆角
                            shape = RoundedCornerShape(20.dp),
                            color = Color.White,
                            tonalElevation = 0.dp
                        ) {
                            CodePreviewWebView(
                                code = code,
                                language = language
                            )
                        }
                        
                        // 底部按钮区域
                        Surface(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(8.dp)
                                .clip(RoundedCornerShape(20.dp)),
                            color = Color(0xFFF5F5F5),
                            tonalElevation = 2.dp
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(16.dp),
                                horizontalArrangement = Arrangement.Center
                            ) {
                                Button(
                                    onClick = {
                                        openInExternalBrowser(context, code, language)
                                    },
                                    colors = ButtonDefaults.buttonColors(
                                        containerColor = Color(0xFF2196F3),
                                        contentColor = Color.White
                                    ),
                                    shape = RoundedCornerShape(12.dp)
                                ) {
                                    Icon(
                                        Icons.Filled.OpenInBrowser,
                                        contentDescription = "用浏览器打开",
                                        modifier = Modifier.size(18.dp)
                                    )
                                    Spacer(modifier = Modifier.width(8.dp))
                                    Text(
                                        text = "用浏览器打开",
                                        style = MaterialTheme.typography.bodyMedium.copy(
                                            fontWeight = FontWeight.Medium
                                        )
                                    )
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

/**
 * WebView组件用于渲染代码预览
 */
@Composable
private fun CodePreviewWebView(
    code: String,
    language: String?
) {
    val context = LocalContext.current
    
    AndroidView(
        factory = { context ->
            WebView(context).apply {
                webViewClient = object : WebViewClient() {
                    override fun onPageFinished(view: WebView?, url: String?) {
                        super.onPageFinished(view, url)
                        // 页面加载完成后的回调
                        android.util.Log.d("CodePreview", "Page finished loading: $url")
                    }

                    override fun onPageStarted(view: WebView?, url: String?, favicon: android.graphics.Bitmap?) {
                        super.onPageStarted(view, url, favicon)
                        android.util.Log.d("CodePreview", "Page started loading: $url")
                    }

                    override fun onReceivedError(
                        view: WebView?,
                        errorCode: Int,
                        description: String?,
                        failingUrl: String?
                    ) {
                        super.onReceivedError(view, errorCode, description, failingUrl)
                        android.util.Log.e("CodePreview", "WebView error: $errorCode - $description")
                        // 处理加载错误
                        view?.loadData(
                            """
                            <html><body style="font-family: system-ui; padding: 20px; text-align: center; background: #ffebee;">
                            <h3 style="color: #f44336;">预览加载失败</h3>
                            <p>错误代码: $errorCode</p>
                            <p>错误描述: $description</p>
                            <p>URL: $failingUrl</p>
                            </body></html>
                            """.trimIndent(),
                            "text/html",
                            "UTF-8"
                        )
                    }

                    override fun onReceivedHttpError(
                        view: WebView?,
                        request: android.webkit.WebResourceRequest?,
                        errorResponse: android.webkit.WebResourceResponse?
                    ) {
                        super.onReceivedHttpError(view, request, errorResponse)
                        android.util.Log.e("CodePreview", "HTTP error: ${errorResponse?.statusCode} - ${errorResponse?.reasonPhrase}")
                    }
                }
                settings.apply {
                    javaScriptEnabled = true
                    domStorageEnabled = true
                    allowFileAccess = false
                    allowContentAccess = false
                    allowUniversalAccessFromFileURLs = false
                    allowFileAccessFromFileURLs = false
                    mixedContentMode = android.webkit.WebSettings.MIXED_CONTENT_ALWAYS_ALLOW
                    cacheMode = android.webkit.WebSettings.LOAD_NO_CACHE
                    useWideViewPort = true
                    loadWithOverviewMode = true
                    setSupportZoom(true)
                    builtInZoomControls = false
                    displayZoomControls = false
                }
            }
        },
        update = { webView ->
            try {
                android.util.Log.d("CodePreview", "Loading content for language: $language")
                android.util.Log.d("CodePreview", "Code length: ${code.length}")

                // 使用最简单的HTML，确保能够显示
                val simpleHtml = """
                <html>
                <body style="margin:0; padding:20px; background-color:#4CAF50; color:white; font-family:Arial; text-align:center; font-size:18px;">
                    <h1 style="margin:20px 0; font-size:24px;">✅ 成功!</h1>
                    <p style="margin:10px 0;">WebView 正在工作</p>
                    <p style="margin:10px 0;">语言: ${language ?: "未知"}</p>
                    <p style="margin:10px 0;">代码长度: ${code.length}</p>
                    <div style="background-color:rgba(255,255,255,0.2); padding:15px; margin:20px 0; border-radius:8px;">
                        <p style="margin:5px 0;">如果您看到这个绿色页面，</p>
                        <p style="margin:5px 0;">说明代码预览功能已经修复！</p>
                    </div>
                </body>
                </html>
                """.trimIndent()

                android.util.Log.d("CodePreview", "Loading simple HTML content")
                android.util.Log.d("CodePreview", "HTML length: ${simpleHtml.length}")

                // 使用最基本的加载方式
                webView.loadData(simpleHtml, "text/html; charset=UTF-8", "UTF-8")

            } catch (e: Exception) {
                android.util.Log.e("CodePreview", "Exception in update: ${e.message}", e)
                // 如果加载失败，显示错误信息
                val errorHtml = "<html><body style='background:red;color:white;padding:20px;text-align:center;'><h2>错误: ${e.message}</h2></body></html>"
                webView.loadData(errorHtml, "text/html; charset=UTF-8", "UTF-8")
            }
        }
    )
}

/**
 * 生成预览用的HTML内容
 */
private fun generatePreviewHtml(code: String, language: String?, errors: List<String> = emptyList()): String {
    // 预处理代码，支持Markdown和数学公式渲染
    val processedCode = preprocessCodeForRendering(code, language)
    
    return when (language?.lowercase()) {
        "html" -> {
            // 生成错误显示HTML
            val errorHtml = if (errors.isNotEmpty()) {
                """
                <div class="error-section">
                    <h4 style="color: #d32f2f; margin: 0 0 8px 0; display: flex; align-items: center;">
                        <span style="margin-right: 8px;">⚠️</span>
                        检测到 ${errors.size} 个问题
                    </h4>
                    ${errors.joinToString("") { "<div class=\"error-item\">• $it</div>" }}
                </div>
                """.trimIndent()
            } else ""
            
            // 如果是完整的HTML文档，只显示预览效果
            if (code.contains("<html", ignoreCase = true) || code.contains("<!doctype", ignoreCase = true)) {
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>HTML Preview</title>
                    <style>
                        body { 
                            margin: 0; 
                            padding: 0;
                            font-family: system-ui, -apple-system, sans-serif; 
                            background: #f5f5f5;
                            line-height: 1.6;
                        }
                        .error-section {
                            background: #ffebee;
                            border: 1px solid #f44336;
                            border-radius: 8px;
                            padding: 16px;
                            margin: 16px;
                            box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
                        }
                        .error-item {
                            color: #d32f2f;
                            margin: 4px 0;
                            font-size: 14px;
                        }
                        .preview-container {
                            width: 100%;
                            height: 100vh;
                            overflow: auto;
                        }
                    </style>
                </head>
                <body>
                    $errorHtml
                    <div class="preview-container">
                        $processedCode
                    </div>
                </body>
                </html>
                """.trimIndent()
            } else {
                // 如果只是HTML片段，包装在完整的HTML文档中
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>HTML Preview</title>
                    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
                    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
                    <script>
                        window.MathJax = {
                            tex: {
                                inlineMath: [['$', '$'], ['\\(', '\\)']],
                                displayMath: [['$$', '$$'], ['\\[', '\\]']]
                            }
                        };
                    </script>
                    <style>
                        body { 
                            margin: 0; 
                            padding: 16px;
                            font-family: system-ui, -apple-system, sans-serif; 
                            background: #f5f5f5;
                            line-height: 1.6;
                        }
                        .error-section {
                            background: #ffebee;
                            border: 1px solid #f44336;
                            border-radius: 8px;
                            padding: 16px;
                            margin-bottom: 16px;
                            box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
                        }
                        .error-item {
                            color: #d32f2f;
                            margin: 4px 0;
                            font-size: 14px;
                        }
                        .content-section {
                            background: white;
                            border-radius: 8px;
                            padding: 16px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            min-height: calc(100vh - 32px);
                        }
                    </style>
                </head>
                <body>
                    $errorHtml
                    <div class="content-section">
                        $processedCode
                    </div>
                </body>
                </html>
                """.trimIndent()
            }
        }
        "svg", "xml" -> {
            // 对于XML，检查是否是SVG内容
            if (language?.lowercase() == "xml" && !code.contains("<svg", ignoreCase = true)) {
                // 生成错误显示HTML
                val errorHtml = if (errors.isNotEmpty()) {
                    """
                    <div class="error-section">
                        <h4 style="color: #d32f2f; margin: 0 0 8px 0; display: flex; align-items: center;">
                            <span style="margin-right: 8px;">⚠️</span>
                            检测到 ${errors.size} 个问题
                        </h4>
                        ${errors.joinToString("") { "<div class=\"error-item\">• $it</div>" }}
                    </div>
                    """.trimIndent()
                } else ""
                
                // 如果是XML但不是SVG，显示友好提示信息
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>XML Preview</title>
                    <style>
                        body { 
                            margin: 0; 
                            padding: 20px;
                            font-family: system-ui, -apple-system, sans-serif; 
                            background: #f5f5f5;
                            line-height: 1.6;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                        }
                        .message-container {
                            background: white;
                            border-radius: 8px;
                            padding: 40px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            text-align: center;
                            max-width: 500px;
                        }
                        .icon {
                            font-size: 48px;
                            margin-bottom: 16px;
                        }
                        h2 {
                            color: #333;
                            margin-bottom: 16px;
                        }
                        p {
                            color: #666;
                            margin-bottom: 0;
                        }
                        .error-section {
                            background: #ffebee;
                            border: 1px solid #f44336;
                            border-radius: 8px;
                            padding: 16px;
                            margin-bottom: 20px;
                            box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
                        }
                        .error-item {
                            color: #d32f2f;
                            margin: 4px 0;
                            font-size: 14px;
                        }
                    </style>
                </head>
                <body>
                    <div class="message-container">
                        $errorHtml
                        <div class="icon">📄</div>
                        <h2>XML 内容</h2>
                        <p>此内容为 XML 格式，无法直接预览。如需查看内容，请在聊天界面中查看原始文本。</p>
                    </div>
                </body>
                </html>
                """.trimIndent()
            } else {
                // SVG内容（支持SVG中的文本Markdown渲染）
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>SVG Preview</title>
                    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
                    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
                    <script>
                        window.MathJax = {
                            tex: {
                                inlineMath: [['$', '$'], ['\\(', '\\)']],
                                displayMath: [['$$', '$$'], ['\\[', '\\]']]
                            }
                        };
                    </script>
                    <style>
                        body { 
                            margin: 0; 
                            padding: 16px;
                            font-family: system-ui, -apple-system, sans-serif; 
                            background: #f5f5f5;
                            line-height: 1.6;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                        }
                        .svg-container {
                            background: white;
                            border-radius: 8px;
                            padding: 20px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            max-width: 100%;
                            max-height: 100%;
                            overflow: auto;
                        }
                        svg {
                            max-width: 100%;
                            height: auto;
                        }
                    </style>
                </head>
                <body>
                    <div class="svg-container">
                        $processedCode
                    </div>
                </body>
                </html>
                """.trimIndent()
            }
        }
        "svg", "xml" -> {
            // 对于XML，检查是否是SVG内容
            if (language?.lowercase() == "xml" && !code.contains("<svg", ignoreCase = true)) {
                // 生成错误显示HTML
                val errorHtml = if (errors.isNotEmpty()) {
                    """
                    <div class="error-section">
                        <h4 style="color: #d32f2f; margin: 0 0 8px 0; display: flex; align-items: center;">
                            <span style="margin-right: 8px;">⚠️</span>
                            检测到 ${errors.size} 个问题
                        </h4>
                        ${errors.joinToString("") { "<div class=\"error-item\">• $it</div>" }}
                    </div>
                    """.trimIndent()
                } else ""

                // 如果是XML但不是SVG，显示友好提示信息
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>XML Preview</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 20px;
                            font-family: system-ui, -apple-system, sans-serif;
                            background: #f5f5f5;
                            line-height: 1.6;
                        }
                        .error-section {
                            background: #ffebee;
                            border: 1px solid #f44336;
                            border-radius: 8px;
                            padding: 16px;
                            margin-bottom: 16px;
                            box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
                        }
                        .error-item {
                            color: #d32f2f;
                            margin: 4px 0;
                            font-size: 14px;
                        }
                        .content {
                            background: white;
                            padding: 20px;
                            border-radius: 8px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            text-align: center;
                        }
                        h3 {
                            color: #666;
                            margin-bottom: 16px;
                        }
                        p {
                            color: #888;
                            margin: 8px 0;
                        }
                    </style>
                </head>
                <body>
                    $errorHtml
                    <div class="content">
                        <h3>XML 内容预览</h3>
                        <p>此类型的内容无法直接预览</p>
                        <p>请在聊天界面中查看原始文本</p>
                    </div>
                </body>
                </html>
                """.trimIndent()
            } else {
                // SVG内容（支持SVG中的文本Markdown渲染）
                """
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>SVG Preview</title>
                    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
                    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
                    <script>
                        window.MathJax = {
                            tex: {
                                inlineMath: [['$', '$'], ['\\(', '\\)']],
                                displayMath: [['$$', '$$'], ['\\[', '\\]']]
                            }
                        };
                    </script>
                    <style>
                        body {
                            margin: 0;
                            padding: 16px;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            min-height: 100vh;
                            background: #f5f5f5;
                        }
                        svg {
                            max-width: 100%;
                            max-height: 80vh;
                            background: white;
                            border-radius: 8px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        }
                        .description {
                            background: white;
                            padding: 16px;
                            border-radius: 8px;
                            margin-top: 10px;
                            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                            max-width: 600px;
                        }
                    </style>
                </head>
                <body>
                    $processedCode
                    <div class="description" id="description"></div>
                    <script>
                        // 提取SVG中的描述文本并渲染Markdown
                        const svgElement = document.querySelector('svg');
                        if (svgElement) {
                            const titleElement = svgElement.querySelector('title');
                            const descElement = svgElement.querySelector('desc');
                            let description = '';
                            if (titleElement) description += '**' + titleElement.textContent + '**\\n\\n';
                            if (descElement) description += descElement.textContent;
                            
                            if (description.trim()) {
                                document.getElementById('description').innerHTML = marked.parse(description);
                                if (window.MathJax) {
                                    MathJax.typesetPromise([document.getElementById('description')]);
                                }
                            } else {
                                document.getElementById('description').style.display = 'none';
                            }
                        }
                    </script>
                </body>
                </html>
                """.trimIndent()
            }
        }
        "markdown", "md" -> {
            // Markdown渲染
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Markdown Preview</title>
                <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.6;
                        background: #f5f5f5;
                    }
                    .content {
                        max-width: 800px;
                        margin: 0 auto;
                        background: white;
                        padding: 30px;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                    h1, h2, h3 { color: #333; }
                    code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
                    pre { background: #f4f4f4; padding: 15px; border-radius: 5px; overflow-x: auto; }
                    blockquote { border-left: 4px solid #ddd; margin: 0; padding-left: 20px; color: #666; }
                </style>
            </head>
            <body>
                <div class="content" id="content"></div>
                <script>
                    const markdown = `${code.replace("`", "\\`")}`;
                    document.getElementById('content').innerHTML = marked.parse(markdown);
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "mermaid" -> {
            // Mermaid图表
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Mermaid Diagram</title>
                <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        display: flex; 
                        justify-content: center; 
                        align-items: center; 
                        min-height: 100vh;
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                    }
                    .mermaid {
                        background: white;
                        padding: 20px;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                </style>
            </head>
            <body>
                <div class="mermaid">
                    $code
                </div>
                <script>
                    mermaid.initialize({ startOnLoad: true, theme: 'default' });
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "plantuml", "puml", "uml" -> {
            // PlantUML图表
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>PlantUML Diagram</title>
                <style>
                    body { 
                        margin: 0; 
                        padding: 0;
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        min-height: 100vh;
                    }
                    .message-container {
                        background: white;
                        border-radius: 16px;
                        padding: 32px;
                        text-align: center;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                        max-width: 400px;
                        margin: 16px;
                    }
                    .icon {
                        font-size: 48px;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #333;
                        font-size: 20px;
                        font-weight: 600;
                        margin-bottom: 12px;
                    }
                    .description {
                        color: #666;
                        font-size: 16px;
                        line-height: 1.5;
                    }
                </style>
            </head>
            <body>
                <div class="message-container">
                    <div class="icon">📊</div>
                    <div class="title">PlantUML 图表</div>
                    <div class="description">PlantUML 图表需要服务器端渲染，无法在此处直接预览。请在聊天界面中查看原始代码。</div>
                </div>
            </body>
            </html>
            """.trimIndent()
        }
        "d3", "d3js" -> {
            // D3.js可视化
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>D3.js Visualization</title>
                <script src="https://d3js.org/d3.v7.min.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: Arial, sans-serif;
                        background: #f5f5f5;
                    }
                    #visualization {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                        text-align: center;
                    }
                </style>
            </head>
            <body>
                <div id="visualization">
                    <h3>D3.js 可视化</h3>
                </div>
                <script>
                    try {
                        $code
                    } catch (error) {
                        document.getElementById('visualization').innerHTML += '<div style="color: red; margin-top: 20px;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "p5", "p5js" -> {
            // P5.js创意编程
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>P5.js Sketch</title>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/p5.js/1.7.0/p5.min.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        display: flex; 
                        justify-content: center; 
                        align-items: center; 
                        min-height: 100vh;
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                    }
                    main {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                    }
                </style>
            </head>
            <body>
                <main>
                    <h3 style="text-align: center; margin-top: 0;">P5.js 动画</h3>
                </main>
                <script>
                    try {
                        $code
                    } catch (error) {
                        document.body.innerHTML += '<div style="color: red; text-align: center; margin-top: 20px;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "three", "threejs" -> {
            // Three.js 3D图形
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Three.js 3D Scene</title>
                <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                    }
                    #container {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                        text-align: center;
                    }
                    canvas {
                        border-radius: 4px;
                    }
                </style>
            </head>
            <body>
                <div id="container">
                    <h3>Three.js 3D 场景</h3>
                </div>
                <script>
                    try {
                        $code
                    } catch (error) {
                        document.getElementById('container').innerHTML += '<div style="color: red; margin-top: 20px;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "chartjs", "chart" -> {
            // Chart.js图表
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Chart.js Visualization</title>
                <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                    }
                    .chart-container {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    canvas {
                        max-width: 100%;
                    }
                </style>
            </head>
            <body>
                <div class="chart-container">
                    <h3 style="text-align: center; margin-top: 0;">Chart.js 图表</h3>
                    <canvas id="myChart"></canvas>
                </div>
                <script>
                    try {
                        $code
                    } catch (error) {
                        document.querySelector('.chart-container').innerHTML += '<div style="color: red; margin-top: 20px;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "canvas" -> {
            // HTML5 Canvas
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Canvas Drawing</title>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        display: flex; 
                        justify-content: center; 
                        align-items: center; 
                        min-height: 100vh;
                        background: #f5f5f5;
                        font-family: Arial, sans-serif;
                    }
                    .canvas-container {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                        text-align: center;
                    }
                    canvas {
                        border: 1px solid #ddd;
                        border-radius: 4px;
                    }
                </style>
            </head>
            <body>
                <div class="canvas-container">
                    <h3>Canvas 绘图</h3>
                    <canvas id="canvas" width="400" height="300"></canvas>
                </div>
                <script>
                    try {
                        const canvas = document.getElementById('canvas');
                        const ctx = canvas.getContext('2d');
                        $code
                    } catch (error) {
                        document.querySelector('.canvas-container').innerHTML += '<div style="color: red; margin-top: 20px;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "latex", "tex" -> {
            // LaTeX数学公式
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>LaTeX Math</title>
                <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
                <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
                <script>
                    window.MathJax = {
                        tex: {
                            inlineMath: [['$', '$'], ['\\(', '\\)']],
                            displayMath: [['$$', '$$'], ['\\[', '\\]']]
                        }
                    };
                </script>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: 'Times New Roman', serif;
                        background: #f5f5f5;
                        line-height: 1.6;
                    }
                    .math-container {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 30px;
                        max-width: 800px;
                        margin: 0 auto;
                    }
                </style>
            </head>
            <body>
                <div class="math-container">
                    <h3>LaTeX 数学公式</h3>
                    <div id="math-content">
                        $code
                    </div>
                </div>
            </body>
            </html>
            """.trimIndent()
        }
        "json" -> {
            // JSON数据可视化
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>JSON Data Visualization</title>
                <style>
                    body { 
                        margin: 0; 
                        padding: 20px; 
                        font-family: 'Monaco', 'Menlo', monospace;
                        background: #f5f5f5;
                    }
                    .json-container {
                        background: white;
                        border-radius: 8px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                        padding: 20px;
                        max-width: 800px;
                        margin: 0 auto;
                    }
                    pre {
                        background: #f8f9fa;
                        padding: 15px;
                        border-radius: 5px;
                        overflow-x: auto;
                        border-left: 4px solid #007bff;
                    }
                    .json-key { color: #d73a49; }
                    .json-string { color: #032f62; }
                    .json-number { color: #005cc5; }
                    .json-boolean { color: #e36209; }
                </style>
            </head>
            <body>
                <div class="json-container">
                    <h3>JSON 数据预览</h3>
                    <pre id="json-display"></pre>
                </div>
                <script>
                    try {
                        const jsonData = $code;
                        const formatted = JSON.stringify(jsonData, null, 2);
                        document.getElementById('json-display').textContent = formatted;
                    } catch (error) {
                        document.getElementById('json-display').innerHTML = '<span style="color: red;">JSON格式错误: ' + error.message + '</span>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        "css" -> {
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>CSS Preview</title>
                <style>
                    $code
                </style>
            </head>
            <body>
                <div style="padding: 16px;">
                    <h1>CSS样式预览</h1>
                    <p>这是一个段落文本，用于展示CSS样式效果。</p>
                    <div class="demo-box" style="width: 200px; height: 100px; background: #e3f2fd; border: 1px solid #2196f3; margin: 16px 0; padding: 16px;">
                        演示容器
                    </div>
                    <button style="padding: 8px 16px; margin: 4px;">按钮示例</button>
                    <ul>
                        <li>列表项 1</li>
                        <li>列表项 2</li>
                        <li>列表项 3</li>
                    </ul>
                </div>
            </body>
            </html>
            """.trimIndent()
        }
        "javascript", "js" -> {
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>JavaScript Preview</title>
                <style>
                    body { 
                        margin: 0; 
                        padding: 16px; 
                        font-family: system-ui, -apple-system, sans-serif;
                        background: #f5f5f5;
                    }
                    #output {
                        background: white;
                        border-radius: 8px;
                        padding: 16px;
                        margin-top: 16px;
                        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                    }
                </style>
            </head>
            <body>
                <h2>JavaScript 代码执行结果</h2>
                <div id="output"></div>
                <script>
                    try {
                        // 重定向console.log到页面显示
                        const output = document.getElementById('output');
                        const originalLog = console.log;
                        console.log = function(...args) {
                            const div = document.createElement('div');
                            div.textContent = args.join(' ');
                            div.style.marginBottom = '8px';
                            output.appendChild(div);
                            originalLog.apply(console, args);
                        };
                        
                        // 执行用户代码
                        $code
                    } catch (error) {
                        document.getElementById('output').innerHTML = '<div style="color: red;">错误: ' + error.message + '</div>';
                    }
                </script>
            </body>
            </html>
            """.trimIndent()
        }
        else -> {
            // 生成错误显示HTML
            val errorHtml = if (errors.isNotEmpty()) {
                """
                <div class="error-section">
                    <h4 style="color: #d32f2f; margin: 0 0 8px 0; display: flex; align-items: center;">
                        <span style="margin-right: 8px;">⚠️</span>
                        检测到 ${errors.size} 个问题
                    </h4>
                    ${errors.joinToString("") { "<div class=\"error-item\">• $it</div>" }}
                </div>
                """.trimIndent()
            } else ""
            
            """
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Code Preview</title>
                <style>
                    body { 
                        margin: 0; 
                        padding: 0;
                        font-family: system-ui, -apple-system, sans-serif; 
                        background: #f5f5f5;
                        line-height: 1.6;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        min-height: 100vh;
                    }
                    .error-section {
                        background: #ffebee;
                        border: 1px solid #f44336;
                        border-radius: 8px;
                        padding: 16px;
                        margin: 16px;
                        box-shadow: 0 2px 4px rgba(244, 67, 54, 0.1);
                    }
                    .error-item {
                        color: #d32f2f;
                        margin: 4px 0;
                        font-size: 14px;
                    }
                    .message-container {
                        background: white;
                        border-radius: 16px;
                        padding: 32px;
                        text-align: center;
                        box-shadow: 0 4px 16px rgba(0,0,0,0.1);
                        max-width: 400px;
                        margin: 16px;
                    }
                    .icon {
                        font-size: 48px;
                        margin-bottom: 16px;
                    }
                    .title {
                        color: #333;
                        font-size: 20px;
                        font-weight: 600;
                        margin-bottom: 12px;
                    }
                    .description {
                        color: #666;
                        font-size: 16px;
                        line-height: 1.5;
                    }
                </style>
            </head>
            <body>
                $errorHtml
                <div class="message-container">
                    <div class="icon">📄</div>
                    <div class="title">${language ?: "代码"} 内容</div>
                    <div class="description">此类型的内容无法直接预览，请在聊天界面中查看原始文本。</div>
                </div>
            </body>
            </html>
            """.trimIndent()
        }
    }
}

/**
 * 判断代码是否支持预览
 */
private fun isCodePreviewable(language: String?, code: String): Boolean {
    if (language == null && code.isBlank()) return false
    
    val lang = language?.lowercase()
    
    // 直接支持的语言
    when (lang) {
        // Web技术
        "html", "svg", "css", "javascript", "js" -> return true
        
        // 标记语言
        "markdown", "md" -> return true
        
        // 图表和可视化
        "mermaid" -> return true
        "plantuml", "puml", "uml" -> return true
        "d3", "d3js" -> return true
        "p5", "p5js" -> return true
        "three", "threejs" -> return true
        "chartjs", "chart" -> return true
        
        // 图形和动画
        "canvas" -> return true
        "webgl", "glsl" -> return true
        "processing" -> return true
        
        // 数学和科学
        "latex", "tex" -> return true
        "mathml" -> return true
        
        // 数据格式
        "json" -> {
            // 检查是否是可视化的JSON数据
            if (code.contains("\"type\"", ignoreCase = true) || 
                code.contains("\"data\"", ignoreCase = true) ||
                code.contains("\"chart\"", ignoreCase = true)) {
                return true
            }
        }
        
        "xml" -> {
            // 对于XML，检查是否是SVG或其他可视化格式
            if (code.contains("<svg", ignoreCase = true) ||
                code.contains("<mathml", ignoreCase = true)) {
                return true
            }
        }
    }
    
    // 基于内容的检测
    return code.contains("<html", ignoreCase = true) ||
           code.contains("<svg", ignoreCase = true) ||
           code.contains("<!doctype", ignoreCase = true) ||
           code.contains("graph", ignoreCase = true) && code.contains("-->", ignoreCase = true) || // Mermaid
           code.contains("@startuml", ignoreCase = true) || // PlantUML
           code.contains("d3.select", ignoreCase = true) || // D3.js
           code.contains("createCanvas", ignoreCase = true) || // P5.js
           code.contains("THREE.", ignoreCase = true) || // Three.js
           code.contains("Chart(", ignoreCase = true) || // Chart.js
           code.contains("getContext", ignoreCase = true) || // Canvas
           code.contains("\\begin{", ignoreCase = true) // LaTeX
}

/**
 * 预处理代码内容，支持Markdown和数学公式渲染
 */
private fun preprocessCodeForRendering(code: String, language: String?): String {
    // 对于某些语言，直接返回原始代码
    val directRenderLanguages = setOf("html", "svg", "css", "javascript", "js")
    if (language?.lowercase() in directRenderLanguages) {
        return code
    }
    
    // 对于其他语言，处理其中的Markdown和数学公式
    return processMarkdownAndMath(code)
}

/**
 * 处理文本中的Markdown和数学公式
 */
private fun processMarkdownAndMath(text: String): String {
    var processed = text
    
    // 处理数学公式 - 保持LaTeX格式以便MathJax渲染
    // 这里不需要转换，只需要确保格式正确
    processed = processed.replace(Regex("\\\\\\(([^)]+)\\\\\\)")) { "\\(${it.groupValues[1]}\\)" }
    processed = processed.replace(Regex("\\\\\\[([^\\]]+)\\\\\\]")) { "\\[${it.groupValues[1]}\\]" }
    
    // 处理常见的Markdown语法
    // 粗体
    processed = processed.replace(Regex("\\*\\*([^*]+)\\*\\*")) { "**${it.groupValues[1]}**" }
    // 斜体
    processed = processed.replace(Regex("\\*([^*]+)\\*")) { "*${it.groupValues[1]}*" }
    // 代码
    processed = processed.replace(Regex("`([^`]+)`")) { "`${it.groupValues[1]}`" }
    // 链接
    processed = processed.replace(Regex("\\[([^\\]]+)\\]\\(([^)]+)\\)")) { "[${it.groupValues[1]}](${it.groupValues[2]})" }
    
    return processed
}

/**
 * 检测代码中的错误
 */
private fun detectCodeErrors(code: String, language: String?): List<String> {
    val errors = mutableListOf<String>()
    val lang = language?.lowercase()
    
    when (lang) {
        "html" -> {
            // HTML错误检测
            if (!code.contains("<html", ignoreCase = true) && !code.contains("<!doctype", ignoreCase = true)) {
                // 检查是否有未闭合的标签
                val openTags = Regex("<([a-zA-Z][^>]*)>").findAll(code).map { it.groupValues[1].split(" ")[0] }.toList()
                val closeTags = Regex("</([a-zA-Z][^>]*)>").findAll(code).map { it.groupValues[1] }.toList()
                
                val selfClosingTags = setOf("img", "br", "hr", "input", "meta", "link", "area", "base", "col", "embed", "source", "track", "wbr")
                val unclosedTags = openTags.filter { tag -> 
                    !selfClosingTags.contains(tag.lowercase()) && !closeTags.contains(tag)
                }
                
                if (unclosedTags.isNotEmpty()) {
                    errors.add("未闭合的HTML标签: ${unclosedTags.joinToString(", ")}")
                }
            }
            
            // 检查常见的HTML错误
            if (code.contains("<script", ignoreCase = true) && !code.contains("</script>", ignoreCase = true)) {
                errors.add("script标签未正确闭合")
            }
            if (code.contains("<style", ignoreCase = true) && !code.contains("</style>", ignoreCase = true)) {
                errors.add("style标签未正确闭合")
            }
        }
        
        "css" -> {
            // CSS错误检测
            val openBraces = code.count { it == '{' }
            val closeBraces = code.count { it == '}' }
            if (openBraces != closeBraces) {
                errors.add("CSS大括号不匹配 (开: $openBraces, 闭: $closeBraces)")
            }
            
            // 检查CSS语法错误
            if (code.contains(";{") || code.contains("}{")) {
                errors.add("CSS语法错误：分号或大括号位置不正确")
            }
        }
        
        "javascript", "js" -> {
            // JavaScript错误检测
            val openParens = code.count { it == '(' }
            val closeParens = code.count { it == ')' }
            if (openParens != closeParens) {
                errors.add("JavaScript括号不匹配 (开: $openParens, 闭: $closeParens)")
            }
            
            val openBraces = code.count { it == '{' }
            val closeBraces = code.count { it == '}' }
            if (openBraces != closeBraces) {
                errors.add("JavaScript大括号不匹配 (开: $openBraces, 闭: $closeBraces)")
            }
            
            // 检查常见的JavaScript错误
            if (code.contains("function") && !code.contains("{")) {
                errors.add("函数定义缺少大括号")
            }
        }
        
        "svg", "xml" -> {
            // SVG/XML错误检测
            if (!code.contains("<svg", ignoreCase = true) && lang == "svg") {
                errors.add("SVG代码缺少<svg>根元素")
            }
            
            // 检查XML格式错误
            val openTags = Regex("<([a-zA-Z][^/>]*[^/])>").findAll(code).map { it.groupValues[1].split(" ")[0] }.toList()
            val closeTags = Regex("</([a-zA-Z][^>]*)>").findAll(code).map { it.groupValues[1] }.toList()
            val selfClosingTags = Regex("<([a-zA-Z][^/>]*)/\\s*>").findAll(code).map { it.groupValues[1].split(" ")[0] }.toList()
            
            val unclosedTags = openTags.filter { tag -> 
                !closeTags.contains(tag) && !selfClosingTags.contains(tag)
            }
            
            if (unclosedTags.isNotEmpty()) {
                errors.add("未闭合的XML标签: ${unclosedTags.joinToString(", ")}")
            }
        }
        
        "json" -> {
            // JSON错误检测
            try {
                // 简单的JSON格式检查
                val openBraces = code.count { it == '{' }
                val closeBraces = code.count { it == '}' }
                val openBrackets = code.count { it == '[' }
                val closeBrackets = code.count { it == ']' }
                
                if (openBraces != closeBraces) {
                    errors.add("JSON大括号不匹配")
                }
                if (openBrackets != closeBrackets) {
                    errors.add("JSON方括号不匹配")
                }
                
                // 检查常见的JSON错误
                if (code.contains(",}") || code.contains(",]")) {
                    errors.add("JSON格式错误：多余的逗号")
                }
                if (code.contains("'{") || code.contains("'}")) {
                    errors.add("JSON格式错误：字符串引号使用错误")
                }
            } catch (e: Exception) {
                errors.add("JSON格式错误: ${e.message}")
            }
        }
    }
    
    // 通用错误检测
    if (code.isBlank()) {
        errors.add("代码内容为空")
    }
    
    // 检查是否包含可能的错误标记
    if (code.contains("ERROR", ignoreCase = true) || code.contains("EXCEPTION", ignoreCase = true)) {
        errors.add("代码中包含错误信息")
    }
    
    return errors
}

/**
 * 在外部浏览器中打开代码
 */
private fun openInExternalBrowser(context: android.content.Context, code: String, language: String?) {
    try {
        // 创建临时HTML文件
        val errors = detectCodeErrors(code, language)
        val htmlContent = generatePreviewHtml(code, language, errors)
        val fileName = "code_preview_${System.currentTimeMillis()}.html"
        val file = File(context.cacheDir, fileName)
        
        FileOutputStream(file).use { outputStream ->
            outputStream.write(htmlContent.toByteArray())
        }
        
     // 使用FileProvider获取安全的URI
        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.provider",
            file
        )
        
        // 创建Intent打开文件
        val intent = Intent(Intent.ACTION_VIEW).apply {
            setDataAndType(uri, "text/html")
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
        }
        
        // 尝试打开浏览器
        try {
            context.startActivity(intent)
        } catch (e: Exception) {
            // 如果没有找到合适的应用，尝试使用通用的Intent
            val genericIntent = Intent(Intent.ACTION_VIEW, uri).apply {
                flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_GRANT_READ_URI_PERMISSION
            }
            context.startActivity(Intent.createChooser(genericIntent, "选择浏览器打开"))
        }
    } catch (e: Exception) {
        // 错误处理 - 可以显示Toast或其他错误提示
        e.printStackTrace()
    }
}